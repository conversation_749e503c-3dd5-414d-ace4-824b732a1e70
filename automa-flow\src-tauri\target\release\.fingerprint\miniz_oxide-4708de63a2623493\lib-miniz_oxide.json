{"rustc": 1842507548689473721, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 15743642140631288636, "path": 14936214404469579842, "deps": [[4018467389006652250, "simd_adler32", false, 11752655103114171599], [7911289239703230891, "adler2", false, 14381707902724454479]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-4708de63a2623493\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}