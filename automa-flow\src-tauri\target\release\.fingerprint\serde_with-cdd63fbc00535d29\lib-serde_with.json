{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5456902659710135487, "path": 2988340088912439635, "deps": [[7026957619838884710, "serde_with_macros", false, 3465962526902101523], [9689903380558560274, "serde", false, 11097669078372303768], [16257276029081467297, "serde_derive", false, 4005277621064045498]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\serde_with-cdd63fbc00535d29\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}