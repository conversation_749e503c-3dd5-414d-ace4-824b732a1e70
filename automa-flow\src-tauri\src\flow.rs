use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::mpsc;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;

use crate::error::{AutomaError, AutomaResult};
use crate::message::{Message, MessagePayload, NodeId};
use crate::node::{Node, NodeConfig, NodeContext, NodeMessage, NodeRegistry, NodeStatus};

/// 流程ID类型
pub type FlowId = String;

/// 连接定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Connection {
    /// 来源节点ID
    pub source_node: NodeId,
    /// 来源端口
    pub source_port: usize,
    /// 目标节点ID
    pub target_node: NodeId,
    /// 目标端口
    pub target_port: usize,
}

/// 流程定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlowDefinition {
    /// 流程ID
    pub id: FlowId,
    /// 流程名称
    pub name: String,
    /// 流程描述
    pub description: Option<String>,
    /// 节点配置列表
    pub nodes: Vec<NodeConfig>,
    /// 连接列表
    pub connections: Vec<Connection>,
    /// 流程属性
    pub properties: HashMap<String, serde_json::Value>,
}

/// 流程状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FlowStatus {
    /// 未初始化
    Uninitialized,
    /// 已初始化
    Initialized,
    /// 运行中
    Running,
    /// 已暂停
    Paused,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
}

/// 节点实例
struct NodeInstance {
    /// 节点实现
    node: Box<dyn Node>,
    /// 节点上下文
    context: NodeContext,
    /// 节点任务句柄
    task_handle: Option<JoinHandle<()>>,
}

/// 流程实例
pub struct Flow {
    /// 流程定义
    definition: FlowDefinition,
    /// 流程状态
    status: FlowStatus,
    /// 节点实例映射
    nodes: HashMap<NodeId, NodeInstance>,
    /// 连接映射：source_node -> [(source_port, target_node, target_port)]
    connections: HashMap<NodeId, Vec<(usize, NodeId, usize)>>,
    /// 消息通道
    message_sender: mpsc::UnboundedSender<NodeMessage>,
    message_receiver: Option<mpsc::UnboundedReceiver<NodeMessage>>,
    /// 流程任务句柄
    flow_task: Option<JoinHandle<()>>,
}

impl Flow {
    /// 创建新的流程实例
    pub fn new(definition: FlowDefinition, registry: &NodeRegistry) -> AutomaResult<Self> {
        let (message_sender, message_receiver) = mpsc::unbounded_channel();
        
        // 构建连接映射
        let mut connections: HashMap<NodeId, Vec<(usize, NodeId, usize)>> = HashMap::new();
        for conn in &definition.connections {
            connections
                .entry(conn.source_node.clone())
                .or_insert_with(Vec::new)
                .push((conn.source_port, conn.target_node.clone(), conn.target_port));
        }

        // 创建节点实例
        let mut nodes = HashMap::new();
        for node_config in &definition.nodes {
            let node = registry.create_node(node_config)?;
            let context = NodeContext::new(node_config.clone(), message_sender.clone());
            
            let instance = NodeInstance {
                node,
                context,
                task_handle: None,
            };
            
            nodes.insert(node_config.id.clone(), instance);
        }

        Ok(Self {
            definition,
            status: FlowStatus::Uninitialized,
            nodes,
            connections,
            message_sender,
            message_receiver: Some(message_receiver),
            flow_task: None,
        })
    }

    /// 初始化流程
    pub async fn initialize(&mut self) -> AutomaResult<()> {
        if self.status != FlowStatus::Uninitialized {
            return Err(AutomaError::FlowError(
                "Flow is already initialized".to_string(),
            ));
        }

        // 初始化所有节点
        for (node_id, instance) in &mut self.nodes {
            match instance.node.initialize(&mut instance.context).await {
                Ok(_) => {
                    println!("Node {} initialized successfully", node_id);
                }
                Err(e) => {
                    return Err(AutomaError::FlowError(format!(
                        "Failed to initialize node {}: {}",
                        node_id, e
                    )));
                }
            }
        }

        self.status = FlowStatus::Initialized;
        Ok(())
    }

    /// 启动流程
    pub async fn start(&mut self) -> AutomaResult<()> {
        if self.status != FlowStatus::Initialized && self.status != FlowStatus::Stopped {
            return Err(AutomaError::FlowError(format!(
                "Cannot start flow in status: {:?}",
                self.status
            )));
        }

        // 启动所有节点
        for (node_id, instance) in &mut self.nodes {
            match instance.node.start(&mut instance.context).await {
                Ok(_) => {
                    println!("Node {} started successfully", node_id);
                }
                Err(e) => {
                    return Err(AutomaError::FlowError(format!(
                        "Failed to start node {}: {}",
                        node_id, e
                    )));
                }
            }
        }

        // 启动消息处理循环
        if let Some(mut receiver) = self.message_receiver.take() {
            let connections = self.connections.clone();
            let sender = self.message_sender.clone();
            
            let task = tokio::spawn(async move {
                while let Some(message) = receiver.recv().await {
                    if let Err(e) = Self::handle_message(message, &connections, &sender).await {
                        eprintln!("Error handling message: {}", e);
                    }
                }
            });
            
            self.flow_task = Some(task);
        }

        self.status = FlowStatus::Running;

        // 启动入口节点
        self.start_entry_nodes().await?;

        Ok(())
    }

    /// 启动入口节点
    async fn start_entry_nodes(&mut self) -> AutomaResult<()> {
        // 找到所有入口节点（没有输入连接的节点）
        let mut entry_nodes = Vec::new();

        for node_config in &self.definition.nodes {
            let has_input = self.definition.connections.iter()
                .any(|conn| conn.target_node == node_config.id);

            if !has_input {
                entry_nodes.push(node_config.id.clone());
            }
        }

        // 为入口节点发送初始消息
        for node_id in entry_nodes {
            let initial_message = Message::new(MessagePayload::Null);
            let node_message = NodeMessage::Input {
                node_id: node_id.clone(),
                message: initial_message,
                port: 0,
            };

            if let Err(e) = self.message_sender.send(node_message) {
                eprintln!("Failed to send initial message to entry node {}: {}", node_id, e);
            }
        }

        Ok(())
    }

    /// 停止流程
    pub async fn stop(&mut self) -> AutomaResult<()> {
        if self.status != FlowStatus::Running && self.status != FlowStatus::Paused {
            return Err(AutomaError::FlowError(format!(
                "Cannot stop flow in status: {:?}",
                self.status
            )));
        }

        // 停止消息处理任务
        if let Some(task) = self.flow_task.take() {
            task.abort();
        }

        // 停止所有节点
        for (node_id, instance) in &mut self.nodes {
            match instance.node.stop(&mut instance.context).await {
                Ok(_) => {
                    println!("Node {} stopped successfully", node_id);
                }
                Err(e) => {
                    eprintln!("Failed to stop node {}: {}", node_id, e);
                }
            }
        }

        self.status = FlowStatus::Stopped;
        Ok(())
    }

    /// 暂停流程
    pub async fn pause(&mut self) -> AutomaResult<()> {
        if self.status != FlowStatus::Running {
            return Err(AutomaError::FlowError(format!(
                "Cannot pause flow in status: {:?}",
                self.status
            )));
        }

        self.status = FlowStatus::Paused;
        println!("Flow {} paused", self.definition.id);
        Ok(())
    }

    /// 恢复流程
    pub async fn resume(&mut self) -> AutomaResult<()> {
        if self.status != FlowStatus::Paused {
            return Err(AutomaError::FlowError(format!(
                "Cannot resume flow in status: {:?}",
                self.status
            )));
        }

        self.status = FlowStatus::Running;
        println!("Flow {} resumed", self.definition.id);
        Ok(())
    }

    /// 处理消息
    async fn handle_message(
        message: NodeMessage,
        connections: &HashMap<NodeId, Vec<(usize, NodeId, usize)>>,
        sender: &mpsc::UnboundedSender<NodeMessage>,
    ) -> AutomaResult<()> {
        match message {
            NodeMessage::Output {
                node_id,
                message: msg,
                port,
            } => {
                println!("Routing message from node {} port {} with payload: {:?}",
                    node_id, port, msg.payload);

                // 查找连接到此输出端口的目标节点
                if let Some(targets) = connections.get(&node_id) {
                    let mut routed_count = 0;

                    for (source_port, target_node, target_port) in targets {
                        if *source_port == port {
                            let input_message = NodeMessage::Input {
                                node_id: target_node.clone(),
                                message: msg.clone_with_new_id(),
                                port: *target_port,
                            };

                            println!("Routing to node {} port {}", target_node, target_port);

                            sender.send(input_message).map_err(|e| {
                                AutomaError::MessageError(format!("Failed to route message: {}", e))
                            })?;

                            routed_count += 1;
                        }
                    }

                    if routed_count == 0 {
                        println!("No targets found for node {} port {}", node_id, port);
                    }
                } else {
                    println!("No connections found for node {}", node_id);
                }
            }
            NodeMessage::Input {
                node_id,
                message: _,
                port,
            } => {
                // 这里应该将消息发送给对应的节点处理
                // 在实际实现中，需要维护节点实例的引用
                println!("Routing message to node {} port {}", node_id, port);
            }
            NodeMessage::Error { node_id, error } => {
                eprintln!("Node {} error: {}", node_id, error);
            }
            NodeMessage::StatusChange { node_id, status } => {
                println!("Node {} status changed to: {:?}", node_id, status);
            }
        }
        
        Ok(())
    }

    /// 获取流程状态
    pub fn get_status(&self) -> &FlowStatus {
        &self.status
    }

    /// 获取流程定义
    pub fn get_definition(&self) -> &FlowDefinition {
        &self.definition
    }

    /// 获取节点状态
    pub fn get_node_status(&self, node_id: &NodeId) -> Option<&NodeStatus> {
        self.nodes.get(node_id).map(|instance| &instance.context.status)
    }

    /// 向指定节点发送消息
    pub fn send_message_to_node(&self, node_id: &NodeId, message: Message) -> AutomaResult<()> {
        let input_message = NodeMessage::Input {
            node_id: node_id.clone(),
            message,
            port: 0, // 默认发送到端口0
        };

        self.message_sender.send(input_message).map_err(|e| {
            AutomaError::MessageError(format!("Failed to send message to node: {}", e))
        })?;

        Ok(())
    }
}
