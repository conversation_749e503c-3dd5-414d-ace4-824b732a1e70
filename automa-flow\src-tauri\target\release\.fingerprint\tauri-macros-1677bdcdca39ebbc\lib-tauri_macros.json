{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 17481861598492978825, "deps": [[654232091421095663, "tauri_utils", false, 3843845961740883395], [2704937418414716471, "tauri_codegen", false, 13874980082270855401], [3060637413840920116, "proc_macro2", false, 5096312196297537516], [4974441333307933176, "syn", false, 3308007116254542709], [13077543566650298139, "heck", false, 1577398889775765422], [17990358020177143287, "quote", false, 9304775612473768952]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-1677bdcdca39ebbc\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}