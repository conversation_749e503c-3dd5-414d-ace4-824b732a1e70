{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 15561909567757332897, "deps": [[654232091421095663, "tauri_utils", false, 3843845961740883395], [1140993874832966859, "tauri_winres", false, 10998089249385064402], [4824857623768494398, "cargo_toml", false, 576679273028687956], [4899080583175475170, "semver", false, 18440904286450592401], [6913375703034175521, "schemars", false, 7724176244912090427], [7170110829644101142, "json_patch", false, 6795241301540751939], [9689903380558560274, "serde", false, 11093209791670419817], [13077543566650298139, "heck", false, 1577398889775765422], [13625485746686963219, "anyhow", false, 17279382658687598212], [15609422047640926750, "toml", false, 18143949488324536827], [15622660310229662834, "walkdir", false, 4267858985654455119], [16362055519698394275, "serde_json", false, 376776532189080624], [16928111194414003569, "dirs", false, 3826384170316598484], [17155886227862585100, "glob", false, 12384589947144232082]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-4722ba94ec492788\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}