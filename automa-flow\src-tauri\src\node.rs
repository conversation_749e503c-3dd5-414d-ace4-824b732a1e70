use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::mpsc;

use crate::error::{AutomaError, AutomaResult};
use crate::message::{Message, NodeId};

/// 节点配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeConfig {
    /// 节点ID
    pub id: NodeId,
    /// 节点类型
    pub node_type: String,
    /// 节点名称
    pub name: Option<String>,
    /// 节点配置参数
    pub config: HashMap<String, serde_json::Value>,
    /// 输入端口数量
    pub input_ports: usize,
    /// 输出端口数量
    pub output_ports: usize,
    /// 节点位置
    pub position: Option<Position>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub x: f64,
    pub y: f64,
}

/// 节点状态
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum NodeStatus {
    /// 未初始化
    Uninitialized,
    /// 已初始化
    Initialized,
    /// 运行中
    Running,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
}

/// 节点执行上下文
#[derive(Debug)]
pub struct NodeContext {
    /// 节点配置
    pub config: NodeConfig,
    /// 消息发送器
    pub sender: mpsc::UnboundedSender<NodeMessage>,
    /// 节点状态
    pub status: NodeStatus,
    /// 节点属性存储
    pub properties: HashMap<String, serde_json::Value>,
}

impl NodeContext {
    pub fn new(config: NodeConfig, sender: mpsc::UnboundedSender<NodeMessage>) -> Self {
        Self {
            config,
            sender,
            status: NodeStatus::Uninitialized,
            properties: HashMap::new(),
        }
    }

    /// 发送消息到指定输出端口
    pub fn send_message(&self, message: Message, output_port: usize) -> AutomaResult<()> {
        if output_port >= self.config.output_ports {
            return Err(AutomaError::NodeError(format!(
                "Invalid output port: {} (max: {})",
                output_port, self.config.output_ports
            )));
        }

        let node_msg = NodeMessage::Output {
            node_id: self.config.id.clone(),
            message,
            port: output_port,
        };

        self.sender.send(node_msg).map_err(|e| {
            AutomaError::MessageError(format!("Failed to send message: {}", e))
        })?;

        Ok(())
    }

    /// 发送错误消息
    pub fn send_error(&self, error: String) -> AutomaResult<()> {
        let node_msg = NodeMessage::Error {
            node_id: self.config.id.clone(),
            error,
        };

        self.sender.send(node_msg).map_err(|e| {
            AutomaError::MessageError(format!("Failed to send error: {}", e))
        })?;

        Ok(())
    }

    /// 设置节点属性
    pub fn set_property(&mut self, key: String, value: serde_json::Value) {
        self.properties.insert(key, value);
    }

    /// 获取节点属性
    pub fn get_property(&self, key: &str) -> Option<&serde_json::Value> {
        self.properties.get(key)
    }

    /// 获取配置参数
    pub fn get_config_value(&self, key: &str) -> Option<&serde_json::Value> {
        self.config.config.get(key)
    }
}

/// 节点消息类型
#[derive(Debug)]
pub enum NodeMessage {
    /// 输入消息
    Input {
        node_id: NodeId,
        message: Message,
        port: usize,
    },
    /// 输出消息
    Output {
        node_id: NodeId,
        message: Message,
        port: usize,
    },
    /// 错误消息
    Error {
        node_id: NodeId,
        error: String,
    },
    /// 状态变更
    StatusChange {
        node_id: NodeId,
        status: NodeStatus,
    },
}

/// 节点特征
#[async_trait]
pub trait Node: Send + Sync {
    /// 初始化节点
    async fn initialize(&mut self, context: &mut NodeContext) -> AutomaResult<()> {
        context.status = NodeStatus::Initialized;
        Ok(())
    }

    /// 处理输入消息
    async fn process(&mut self, message: Message, context: &mut NodeContext) -> AutomaResult<()>;

    /// 启动节点
    async fn start(&mut self, context: &mut NodeContext) -> AutomaResult<()> {
        context.status = NodeStatus::Running;
        Ok(())
    }

    /// 停止节点
    async fn stop(&mut self, context: &mut NodeContext) -> AutomaResult<()> {
        context.status = NodeStatus::Stopped;
        Ok(())
    }

    /// 获取节点类型
    fn node_type(&self) -> &str;

    /// 获取节点描述
    fn description(&self) -> &str {
        "No description available"
    }

    /// 验证节点配置
    fn validate_config(&self, _config: &HashMap<String, serde_json::Value>) -> AutomaResult<()> {
        // 默认实现：不进行验证
        Ok(())
    }
}

/// 节点工厂特征
pub trait NodeFactory: Send + Sync {
    /// 创建节点实例
    fn create_node(&self, config: &NodeConfig) -> AutomaResult<Box<dyn Node>>;

    /// 获取支持的节点类型
    fn supported_types(&self) -> Vec<String>;

    /// 获取节点类型的默认配置
    fn default_config(&self, node_type: &str) -> Option<HashMap<String, serde_json::Value>>;
}

/// 节点注册表
#[derive(Default)]
pub struct NodeRegistry {
    factories: HashMap<String, Arc<dyn NodeFactory>>,
}

impl NodeRegistry {
    pub fn new() -> Self {
        Self {
            factories: HashMap::new(),
        }
    }

    /// 注册节点工厂
    pub fn register_factory(&mut self, factory: Arc<dyn NodeFactory>) {
        for node_type in factory.supported_types() {
            self.factories.insert(node_type, factory.clone());
        }
    }

    /// 创建节点实例
    pub fn create_node(&self, config: &NodeConfig) -> AutomaResult<Box<dyn Node>> {
        let factory = self.factories.get(&config.node_type).ok_or_else(|| {
            AutomaError::NodeError(format!("Unknown node type: {}", config.node_type))
        })?;

        factory.create_node(config)
    }

    /// 获取所有支持的节点类型
    pub fn get_supported_types(&self) -> Vec<String> {
        self.factories.keys().cloned().collect()
    }

    /// 获取节点类型的默认配置
    pub fn get_default_config(&self, node_type: &str) -> Option<HashMap<String, serde_json::Value>> {
        self.factories
            .get(node_type)
            .and_then(|factory| factory.default_config(node_type))
    }
}
