# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-bundle-type"
description = "Enables the bundle_type command without any pre-configured scope."
commands.allow = ["bundle_type"]

[[permission]]
identifier = "deny-bundle-type"
description = "Denies the bundle_type command without any pre-configured scope."
commands.deny = ["bundle_type"]
