use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex as AsyncMutex;

mod runtime;
mod flow;
mod node;
mod message;
mod error;
mod nodes;
pub mod performance_test;

use runtime::Runtime;
use flow::{FlowDefinition, Connection};
use node::{NodeConfig, Position};
// 移除未使用的导入

// 全局运行时实例
static RUNTIME: std::sync::OnceLock<Arc<AsyncMutex<Runtime>>> = std::sync::OnceLock::new();

fn get_runtime() -> Arc<AsyncMutex<Runtime>> {
    RUNTIME.get_or_init(|| {
        let mut runtime = Runtime::new();
        // 注册内置节点类型
        nodes::register_builtin_nodes(runtime.node_registry_mut());
        Arc::new(AsyncMutex::new(runtime))
    }).clone()
}

// 兼容前端的数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct NodeData {
    pub id: String,
    pub node_type: String,
    pub label: String,
    pub config: HashMap<String, serde_json::Value>,
    pub position: Position,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EdgeData {
    pub id: String,
    pub source: String,
    pub target: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FlowData {
    pub id: String,
    pub name: String,
    pub nodes: Vec<NodeData>,
    pub edges: Vec<EdgeData>,
}

// 转换函数：前端数据结构 -> 内部数据结构
impl From<FlowData> for FlowDefinition {
    fn from(flow_data: FlowData) -> Self {
        let nodes = flow_data.nodes.into_iter().map(|node| NodeConfig {
            id: node.id,
            node_type: node.node_type,
            name: Some(node.label),
            config: node.config,
            input_ports: 1,  // 默认值，后续可以根据节点类型调整
            output_ports: 1, // 默认值，后续可以根据节点类型调整
            position: Some(node.position),
        }).collect();

        let connections = flow_data.edges.into_iter().map(|edge| Connection {
            source_node: edge.source,
            source_port: 0, // 默认端口
            target_node: edge.target,
            target_port: 0, // 默认端口
        }).collect();

        FlowDefinition {
            id: flow_data.id,
            name: flow_data.name,
            description: None,
            nodes,
            connections,
            properties: HashMap::new(),
        }
    }
}

// Tauri commands
#[tauri::command]
async fn execute_flow(flow_data: FlowData) -> Result<String, String> {
    let runtime = get_runtime();
    let mut runtime = runtime.lock().await;

    // 转换为内部流程定义
    let flow_def: FlowDefinition = flow_data.into();

    // 执行流程
    match runtime.execute_flow(flow_def).await {
        Ok(result) => Ok(format!("Flow executed successfully: {:?}", result)),
        Err(e) => Err(format!("Flow execution failed: {}", e)),
    }
}

#[tauri::command]
async fn validate_flow(flow_data: FlowData) -> Result<bool, String> {
    let runtime = get_runtime();
    let runtime = runtime.lock().await;

    // 转换为内部流程定义
    let flow_def: FlowDefinition = flow_data.into();

    // 验证流程
    match runtime.validate_flow(&flow_def) {
        Ok(_) => Ok(true),
        Err(e) => {
            println!("Flow validation failed: {}", e);
            Ok(false)
        }
    }
}

#[tauri::command]
fn get_automation_capabilities() -> Result<HashMap<String, Vec<String>>, String> {
    let mut capabilities = HashMap::new();
    capabilities.insert("browser".to_string(), vec![
        "open".to_string(),
        "click".to_string(),
        "input".to_string(),
        "extract".to_string(),
    ]);
    capabilities.insert("desktop".to_string(), vec![
        "click".to_string(),
        "input".to_string(),
        "window".to_string(),
    ]);
    Ok(capabilities)
}

#[tauri::command]
async fn get_flow_status(flow_id: String) -> Result<String, String> {
    let runtime = get_runtime();
    let runtime = runtime.lock().await;

    match runtime.get_flow_status(&flow_id).await {
        Some(status) => Ok(format!("{:?}", status)),
        None => Err(format!("Flow not found: {}", flow_id)),
    }
}

#[tauri::command]
async fn stop_flow(flow_id: String) -> Result<String, String> {
    let runtime = get_runtime();
    let mut runtime = runtime.lock().await;

    match runtime.stop_flow(&flow_id).await {
        Ok(_) => Ok("Flow stopped successfully".to_string()),
        Err(e) => Err(format!("Failed to stop flow: {}", e)),
    }
}

#[tauri::command]
async fn get_runtime_stats() -> Result<HashMap<String, serde_json::Value>, String> {
    let runtime = get_runtime();
    let runtime = runtime.lock().await;

    Ok(runtime.get_runtime_stats().await)
}

#[tauri::command]
async fn initialize_runtime() -> Result<String, String> {
    let runtime = get_runtime();
    let mut runtime = runtime.lock().await;

    match runtime.start().await {
        Ok(_) => Ok("Runtime initialized successfully".to_string()),
        Err(e) => Err(format!("Failed to initialize runtime: {}", e)),
    }
}

#[tauri::command]
async fn get_available_node_types() -> Result<Vec<String>, String> {
    let runtime = get_runtime();
    let runtime = runtime.lock().await;

    Ok(runtime.node_registry().get_supported_types())
}

#[tauri::command]
async fn get_node_config_schema(node_type: String) -> Result<HashMap<String, serde_json::Value>, String> {
    let runtime = get_runtime();
    let runtime = runtime.lock().await;

    match runtime.node_registry().get_default_config(&node_type) {
        Some(config) => Ok(config),
        None => Err(format!("Unknown node type: {}", node_type)),
    }
}

#[tauri::command]
async fn pause_flow(flow_id: String) -> Result<String, String> {
    let runtime = get_runtime();
    let mut runtime = runtime.lock().await;

    match runtime.pause_flow(&flow_id).await {
        Ok(_) => Ok("Flow paused successfully".to_string()),
        Err(e) => Err(format!("Failed to pause flow: {}", e)),
    }
}

#[tauri::command]
async fn resume_flow(flow_id: String) -> Result<String, String> {
    let runtime = get_runtime();
    let mut runtime = runtime.lock().await;

    match runtime.resume_flow(&flow_id).await {
        Ok(_) => Ok("Flow resumed successfully".to_string()),
        Err(e) => Err(format!("Failed to resume flow: {}", e)),
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            execute_flow,
            validate_flow,
            get_automation_capabilities,
            get_flow_status,
            stop_flow,
            get_runtime_stats,
            initialize_runtime,
            get_available_node_types,
            get_node_config_schema,
            pause_flow,
            resume_flow
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_runtime_initialization() {
        let runtime = get_runtime();
        let mut runtime = runtime.lock().await;

        assert!(runtime.start().await.is_ok());
        assert!(runtime.stop().await.is_ok());
    }

    #[tokio::test]
    async fn test_flow_validation() {
        let runtime = get_runtime();
        let runtime = runtime.lock().await;

        // 创建一个简单的流程定义
        let flow_def = FlowDefinition {
            id: "test-flow".to_string(),
            name: "Test Flow".to_string(),
            description: Some("A test flow".to_string()),
            nodes: vec![
                NodeConfig {
                    id: "input1".to_string(),
                    node_type: "input".to_string(),
                    name: Some("Input Node".to_string()),
                    config: HashMap::new(),
                    input_ports: 0,
                    output_ports: 1,
                    position: Some(Position { x: 100.0, y: 100.0 }),
                },
                NodeConfig {
                    id: "output1".to_string(),
                    node_type: "output".to_string(),
                    name: Some("Output Node".to_string()),
                    config: HashMap::new(),
                    input_ports: 1,
                    output_ports: 0,
                    position: Some(Position { x: 300.0, y: 100.0 }),
                },
            ],
            connections: vec![
                Connection {
                    source_node: "input1".to_string(),
                    source_port: 0,
                    target_node: "output1".to_string(),
                    target_port: 0,
                },
            ],
            properties: HashMap::new(),
        };

        assert!(runtime.validate_flow(&flow_def).is_ok());
    }

    #[tokio::test]
    async fn test_node_creation() {
        let runtime = get_runtime();
        let runtime = runtime.lock().await;

        let node_config = NodeConfig {
            id: "test-input".to_string(),
            node_type: "input".to_string(),
            name: Some("Test Input".to_string()),
            config: HashMap::new(),
            input_ports: 0,
            output_ports: 1,
            position: Some(Position { x: 0.0, y: 0.0 }),
        };

        let node = runtime.node_registry().create_node(&node_config);
        assert!(node.is_ok());
    }
}
