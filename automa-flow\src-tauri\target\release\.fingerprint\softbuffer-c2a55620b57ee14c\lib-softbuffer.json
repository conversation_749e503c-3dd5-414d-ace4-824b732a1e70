{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 4872535670476380077, "deps": [[376837177317575824, "build_script_build", false, 3086084335942242657], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [5986029879202738730, "log", false, 7783758905256315459], [10281541584571964250, "windows_sys", false, 2546168200542824213]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-c2a55620b57ee14c\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}