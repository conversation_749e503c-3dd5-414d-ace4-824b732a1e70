//! 性能测试可执行文件
//! 运行系统性能基准测试

use automa_flow_lib::performance_test;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎯 Automa Flow 性能基准测试");
    println!("================================\n");
    
    match performance_test::run_all_benchmarks().await {
        Ok(()) => {
            println!("\n✅ 所有性能测试通过！");
            std::process::exit(0);
        }
        Err(e) => {
            eprintln!("\n❌ 性能测试失败: {}", e);
            std::process::exit(1);
        }
    }
}
