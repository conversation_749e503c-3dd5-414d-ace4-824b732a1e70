{"rustc": 1842507548689473721, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 1369601567987815722, "path": 7599337587338048966, "deps": [[7312356825837975969, "crc32fast", false, 8655029668611275053], [7636735136738807108, "miniz_oxide", false, 16623848488310080136]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\flate2-4d33c5937d81a86c\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}