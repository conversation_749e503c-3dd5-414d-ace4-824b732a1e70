//! 性能测试模块
//! 用于测试系统的性能表现

use crate::flow::{FlowDefinition, Connection};
use crate::node::{NodeConfig, Position};
use crate::runtime::Runtime;
use std::collections::HashMap;
use std::time::{Duration, Instant};
// use tokio::time::sleep; // 暂时不需要

/// 创建一个简单的测试流程
pub fn create_simple_flow(flow_id: String) -> FlowDefinition {
    let mut delay_config = HashMap::new();
    delay_config.insert("duration".to_string(), serde_json::Value::Number(serde_json::Number::from(10)));

    FlowDefinition {
        id: flow_id.clone(),
        name: format!("Simple Flow {}", flow_id),
        description: Some("A simple flow for performance testing".to_string()),
        nodes: vec![
            NodeConfig {
                id: "input".to_string(),
                node_type: "input".to_string(),
                name: Some("Input".to_string()),
                config: HashMap::new(),
                input_ports: 0,
                output_ports: 1,
                position: Some(Position { x: 0.0, y: 0.0 }),
            },
            NodeConfig {
                id: "delay".to_string(),
                node_type: "delay".to_string(),
                name: Some("Delay".to_string()),
                config: delay_config,
                input_ports: 1,
                output_ports: 1,
                position: Some(Position { x: 100.0, y: 0.0 }),
            },
            NodeConfig {
                id: "output".to_string(),
                node_type: "output".to_string(),
                name: Some("Output".to_string()),
                config: HashMap::new(),
                input_ports: 1,
                output_ports: 0,
                position: Some(Position { x: 200.0, y: 0.0 }),
            },
        ],
        connections: vec![
            Connection {
                source_node: "input".to_string(),
                source_port: 0,
                target_node: "delay".to_string(),
                target_port: 0,
            },
            Connection {
                source_node: "delay".to_string(),
                source_port: 0,
                target_node: "output".to_string(),
                target_port: 0,
            },
        ],
        properties: HashMap::new(),
    }
}

/// 测试节点创建性能
pub async fn benchmark_node_creation() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始节点创建性能基准测试...");
    
    let runtime = Runtime::new();
    let node_registry = runtime.node_registry();
    
    let node_counts = vec![10, 50, 100];
    
    for &count in &node_counts {
        let start = Instant::now();
        
        for i in 0..count {
            let node_config = NodeConfig {
                id: format!("benchmark_node_{}", i),
                node_type: "delay".to_string(),
                name: Some(format!("Benchmark Node {}", i)),
                config: HashMap::new(),
                input_ports: 1,
                output_ports: 1,
                position: Some(Position { x: 0.0, y: 0.0 }),
            };

            let result = node_registry.create_node(&node_config);
            if result.is_err() {
                return Err(format!("节点创建失败: {:?}", result.err()).into());
            }
        }
        
        let duration = start.elapsed();
        let avg_time = duration.as_nanos() / count as u128;
        
        println!("📊 创建 {} 个节点耗时: {:?} (平均每个: {} ns)", count, duration, avg_time);
        
        // 性能断言：每个节点创建时间应该少于1ms
        if avg_time >= 1_000_000 {
            return Err(format!("节点创建平均时间过长: {} ns", avg_time).into());
        }
    }
    
    println!("✅ 节点创建性能基准测试完成！");
    Ok(())
}

/// 测试流程验证性能
pub async fn benchmark_flow_validation() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 开始流程验证性能基准测试...");
    
    let runtime = Runtime::new();
    let flow_sizes = vec![3, 5, 10];
    
    for &size in &flow_sizes {
        let flow_def = create_chain_flow(size);
        
        let start = Instant::now();
        let result = runtime.validate_flow(&flow_def);
        let duration = start.elapsed();
        
        if result.is_err() {
            return Err(format!("流程验证失败: {:?}", result.err()).into());
        }
        
        println!("📊 验证 {} 节点流程耗时: {:?}", size, duration);
        
        // 性能断言：验证时间应该少于100ms
        if duration >= Duration::from_millis(100) {
            return Err(format!("流程验证时间过长: {:?}", duration).into());
        }
    }
    
    println!("✅ 流程验证性能基准测试完成！");
    Ok(())
}

/// 测试流程执行启动性能
pub async fn benchmark_flow_execution_startup() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始流程执行启动性能基准测试...");
    
    let mut runtime = Runtime::new();
    runtime.start().await.map_err(|e| format!("Runtime启动失败: {:?}", e))?;
    
    let flow_sizes = vec![3, 5];
    
    for &size in &flow_sizes {
        let flow_def = create_chain_flow(size);
        
        let start = Instant::now();
        let result = runtime.execute_flow(flow_def).await;
        let duration = start.elapsed();
        
        if result.is_err() {
            return Err(format!("流程执行失败: {:?}", result.err()).into());
        }
        
        println!("📊 启动 {} 节点流程耗时: {:?}", size, duration);
        
        // 性能断言：启动时间应该少于500ms
        if duration >= Duration::from_millis(500) {
            return Err(format!("流程启动时间过长: {:?}", duration).into());
        }
        
        // 停止流程以清理资源
        let flow_result = result.unwrap();
        let _ = runtime.stop_flow(&flow_result.flow_id).await;
    }
    
    println!("✅ 流程执行启动性能基准测试完成！");
    Ok(())
}

/// 创建链式流程
fn create_chain_flow(node_count: usize) -> FlowDefinition {
    let mut nodes = Vec::new();
    let mut connections = Vec::new();
    
    for i in 0..node_count {
        let node_id = format!("node_{}", i);
        let node_type = if i == 0 {
            "input"
        } else if i == node_count - 1 {
            "output"
        } else {
            "delay"
        };
        
        let mut config = HashMap::new();
        if node_type == "delay" {
            config.insert("duration".to_string(), serde_json::Value::Number(serde_json::Number::from(1)));
        }
        
        nodes.push(NodeConfig {
            id: node_id.clone(),
            node_type: node_type.to_string(),
            name: Some(format!("Node {}", i)),
            config,
            input_ports: if i == 0 { 0 } else { 1 },
            output_ports: if i == node_count - 1 { 0 } else { 1 },
            position: Some(Position { x: (i * 100) as f64, y: 0.0 }),
        });
        
        // 创建连接（除了最后一个节点）
        if i < node_count - 1 {
            connections.push(Connection {
                source_node: node_id,
                source_port: 0,
                target_node: format!("node_{}", i + 1),
                target_port: 0,
            });
        }
    }
    
    FlowDefinition {
        id: format!("chain_flow_{}", node_count),
        name: format!("Chain Flow with {} nodes", node_count),
        description: Some("A chain flow for benchmarking".to_string()),
        nodes,
        connections,
        properties: HashMap::new(),
    }
}

/// 运行所有性能测试
pub async fn run_all_benchmarks() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎯 开始运行所有性能基准测试...\n");
    
    benchmark_node_creation().await?;
    println!();
    
    benchmark_flow_validation().await?;
    println!();
    
    benchmark_flow_execution_startup().await?;
    println!();
    
    println!("🎉 所有性能基准测试完成！");
    Ok(())
}
