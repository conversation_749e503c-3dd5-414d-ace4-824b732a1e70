use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::error::{AutomaError, AutomaResult};
use crate::flow::{Flow, FlowDefinition, FlowId, FlowStatus};
use crate::node::NodeRegistry;
use crate::message::{Message, MessagePayload};

/// 运行时配置
#[derive(Debug, Clone)]
pub struct RuntimeConfig {
    /// 最大并发流程数
    pub max_concurrent_flows: usize,
    /// 消息队列大小
    pub message_queue_size: usize,
    /// 节点超时时间（毫秒）
    pub node_timeout_ms: u64,
    /// 是否启用调试模式
    pub debug_mode: bool,
}

impl Default for RuntimeConfig {
    fn default() -> Self {
        Self {
            max_concurrent_flows: 10,
            message_queue_size: 1000,
            node_timeout_ms: 30000,
            debug_mode: false,
        }
    }
}

/// 流程执行结果
#[derive(Debug, <PERSON><PERSON>)]
pub struct FlowExecutionResult {
    /// 流程ID
    pub flow_id: FlowId,
    /// 执行状态
    pub status: FlowStatus,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 输出结果
    pub outputs: HashMap<String, MessagePayload>,
    /// 错误信息
    pub error: Option<String>,
}

/// 自动化运行时
pub struct Runtime {
    /// 运行时配置
    config: RuntimeConfig,
    /// 节点注册表
    node_registry: NodeRegistry,
    /// 活动流程映射
    flows: Arc<RwLock<HashMap<FlowId, Flow>>>,
    /// 运行时状态
    is_running: bool,
}

impl Runtime {
    /// 创建新的运行时实例
    pub fn new() -> Self {
        Self::with_config(RuntimeConfig::default())
    }

    /// 使用指定配置创建运行时实例
    pub fn with_config(config: RuntimeConfig) -> Self {
        let mut node_registry = NodeRegistry::new();

        // 注册内置节点类型
        crate::nodes::register_builtin_nodes(&mut node_registry);

        Self {
            config,
            node_registry,
            flows: Arc::new(RwLock::new(HashMap::new())),
            is_running: false,
        }
    }

    /// 获取节点注册表的可变引用
    pub fn node_registry_mut(&mut self) -> &mut NodeRegistry {
        &mut self.node_registry
    }

    /// 获取节点注册表的引用
    pub fn node_registry(&self) -> &NodeRegistry {
        &self.node_registry
    }

    /// 启动运行时
    pub async fn start(&mut self) -> AutomaResult<()> {
        if self.is_running {
            return Err(AutomaError::RuntimeError("Runtime is already running".to_string()));
        }

        self.is_running = true;
        println!("Runtime started successfully");
        Ok(())
    }

    /// 停止运行时
    pub async fn stop(&mut self) -> AutomaResult<()> {
        if !self.is_running {
            return Err(AutomaError::RuntimeError("Runtime is not running".to_string()));
        }

        // 停止所有活动流程
        let mut flows = self.flows.write().await;
        for (flow_id, flow) in flows.iter_mut() {
            if let Err(e) = flow.stop().await {
                eprintln!("Failed to stop flow {}: {}", flow_id, e);
            }
        }
        flows.clear();

        self.is_running = false;
        println!("Runtime stopped successfully");
        Ok(())
    }

    /// 验证流程定义
    pub fn validate_flow(&self, flow_def: &FlowDefinition) -> AutomaResult<()> {
        // 验证流程基本信息
        if flow_def.id.is_empty() {
            return Err(AutomaError::ValidationError("Flow ID cannot be empty".to_string()));
        }

        if flow_def.name.is_empty() {
            return Err(AutomaError::ValidationError("Flow name cannot be empty".to_string()));
        }

        if flow_def.nodes.is_empty() {
            return Err(AutomaError::ValidationError("Flow must contain at least one node".to_string()));
        }

        // 验证节点配置
        for node_config in &flow_def.nodes {
            if node_config.id.is_empty() {
                return Err(AutomaError::ValidationError("Node ID cannot be empty".to_string()));
            }

            if node_config.node_type.is_empty() {
                return Err(AutomaError::ValidationError("Node type cannot be empty".to_string()));
            }

            // 检查节点类型是否支持
            if !self.node_registry.get_supported_types().contains(&node_config.node_type) {
                return Err(AutomaError::ValidationError(format!(
                    "Unsupported node type: {}",
                    node_config.node_type
                )));
            }
        }

        // 验证连接
        let node_ids: std::collections::HashSet<_> = flow_def.nodes.iter().map(|n| &n.id).collect();
        for connection in &flow_def.connections {
            if !node_ids.contains(&connection.source_node) {
                return Err(AutomaError::ValidationError(format!(
                    "Source node not found: {}",
                    connection.source_node
                )));
            }

            if !node_ids.contains(&connection.target_node) {
                return Err(AutomaError::ValidationError(format!(
                    "Target node not found: {}",
                    connection.target_node
                )));
            }

            // 验证端口范围
            let source_node = flow_def.nodes.iter().find(|n| n.id == connection.source_node).unwrap();
            let target_node = flow_def.nodes.iter().find(|n| n.id == connection.target_node).unwrap();

            if connection.source_port >= source_node.output_ports {
                return Err(AutomaError::ValidationError(format!(
                    "Invalid source port {} for node {} (max: {})",
                    connection.source_port, connection.source_node, source_node.output_ports
                )));
            }

            if connection.target_port >= target_node.input_ports {
                return Err(AutomaError::ValidationError(format!(
                    "Invalid target port {} for node {} (max: {})",
                    connection.target_port, connection.target_node, target_node.input_ports
                )));
            }
        }

        Ok(())
    }

    /// 执行流程
    pub async fn execute_flow(&mut self, flow_def: FlowDefinition) -> AutomaResult<FlowExecutionResult> {
        if !self.is_running {
            return Err(AutomaError::RuntimeError("Runtime is not running".to_string()));
        }

        let start_time = std::time::Instant::now();

        // 验证流程
        self.validate_flow(&flow_def)?;

        // 检查并发流程限制
        let flows = self.flows.read().await;
        if flows.len() >= self.config.max_concurrent_flows {
            return Err(AutomaError::RuntimeError(format!(
                "Maximum concurrent flows limit reached: {}",
                self.config.max_concurrent_flows
            )));
        }
        drop(flows);

        // 创建流程实例
        let flow_id = flow_def.id.clone();
        let mut flow = Flow::new(flow_def, &self.node_registry)?;

        // 初始化并启动流程
        flow.initialize().await?;
        flow.start().await?;

        // 将流程添加到活动流程列表
        let mut flows = self.flows.write().await;
        flows.insert(flow_id.clone(), flow);
        drop(flows);

        let execution_time = start_time.elapsed().as_millis() as u64;

        Ok(FlowExecutionResult {
            flow_id,
            status: FlowStatus::Running,
            execution_time_ms: execution_time,
            outputs: HashMap::new(),
            error: None,
        })
    }

    /// 停止指定流程
    pub async fn stop_flow(&mut self, flow_id: &FlowId) -> AutomaResult<()> {
        let mut flows = self.flows.write().await;
        
        if let Some(flow) = flows.get_mut(flow_id) {
            flow.stop().await?;
            flows.remove(flow_id);
            Ok(())
        } else {
            Err(AutomaError::FlowError(format!("Flow not found: {}", flow_id)))
        }
    }

    /// 暂停指定流程
    pub async fn pause_flow(&mut self, flow_id: &FlowId) -> AutomaResult<()> {
        let mut flows = self.flows.write().await;

        if let Some(flow) = flows.get_mut(flow_id) {
            flow.pause().await?;
            Ok(())
        } else {
            Err(AutomaError::FlowError(format!("Flow not found: {}", flow_id)))
        }
    }

    /// 恢复指定流程
    pub async fn resume_flow(&mut self, flow_id: &FlowId) -> AutomaResult<()> {
        let mut flows = self.flows.write().await;

        if let Some(flow) = flows.get_mut(flow_id) {
            flow.resume().await?;
            Ok(())
        } else {
            Err(AutomaError::FlowError(format!("Flow not found: {}", flow_id)))
        }
    }

    /// 获取流程状态
    pub async fn get_flow_status(&self, flow_id: &FlowId) -> Option<FlowStatus> {
        let flows = self.flows.read().await;
        flows.get(flow_id).map(|flow| flow.get_status().clone())
    }

    /// 获取所有活动流程的状态
    pub async fn get_all_flow_status(&self) -> HashMap<FlowId, FlowStatus> {
        let flows = self.flows.read().await;
        flows.iter()
            .map(|(id, flow)| (id.clone(), flow.get_status().clone()))
            .collect()
    }

    /// 向指定流程的节点发送消息
    pub async fn send_message_to_node(
        &self,
        flow_id: &FlowId,
        node_id: &str,
        message: Message,
    ) -> AutomaResult<()> {
        let flows = self.flows.read().await;

        if let Some(flow) = flows.get(flow_id) {
            flow.send_message_to_node(&node_id.to_string(), message)?;
            Ok(())
        } else {
            Err(AutomaError::FlowError(format!("Flow not found: {}", flow_id)))
        }
    }

    /// 获取运行时统计信息
    pub async fn get_runtime_stats(&self) -> HashMap<String, serde_json::Value> {
        let flows = self.flows.read().await;
        let mut stats = HashMap::new();
        
        stats.insert("is_running".to_string(), serde_json::Value::Bool(self.is_running));
        stats.insert("active_flows".to_string(), serde_json::Value::Number(flows.len().into()));
        stats.insert("max_concurrent_flows".to_string(), serde_json::Value::Number(self.config.max_concurrent_flows.into()));
        stats.insert("supported_node_types".to_string(), serde_json::Value::Array(
            self.node_registry.get_supported_types().into_iter().map(serde_json::Value::String).collect()
        ));
        
        stats
    }
}
