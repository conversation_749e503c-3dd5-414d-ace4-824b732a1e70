{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2492539748780646464, "profile": 8731458305071235362, "path": 20409501446796135, "deps": [[5773842482151829625, "automa_flow_lib", false, 6878094865604017614], [5773842482151829625, "build_script_build", false, 4544453976680734415], [8319709847752024821, "uuid", false, 7944341049116928397], [9689903380558560274, "serde", false, 16566773452832211003], [11946729385090170470, "async_trait", false, 3595471579630875588], [12092653563678505622, "tauri", false, 13321518371530043187], [16362055519698394275, "serde_json", false, 6989516467227089149], [16702348383442838006, "tauri_plugin_opener", false, 7849125738123481780], [17531218394775549125, "tokio", false, 5532187379304694370]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\automa-flow-45f150a78a1c53a5\\dep-bin-performance_test", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}