{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 14215436630019529674, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 1994566707935584492], [3060637413840920116, "proc_macro2", false, 5096312196297537516], [3150220818285335163, "url", false, 14388060824830755575], [3191507132440681679, "serde_untagged", false, 7836734278367778372], [4071963112282141418, "serde_with", false, 7693934091514507831], [4899080583175475170, "semver", false, 18440904286450592401], [5986029879202738730, "log", false, 104423116417116893], [6606131838865521726, "ctor", false, 5208806649452027373], [6913375703034175521, "schemars", false, 7724176244912090427], [7170110829644101142, "json_patch", false, 6795241301540751939], [8319709847752024821, "uuid", false, 2659708665708755435], [9010263965687315507, "http", false, 3662191618500152102], [9451456094439810778, "regex", false, 2525954231128846445], [9556762810601084293, "brotli", false, 14777250860789216788], [9689903380558560274, "serde", false, 11093209791670419817], [10806645703491011684, "thiserror", false, 17578129560791753473], [11655476559277113544, "cargo_metadata", false, 15467462394983612661], [11989259058781683633, "dunce", false, 15719908978251450929], [13625485746686963219, "anyhow", false, 17279382658687598212], [14232843520438415263, "html5ever", false, 15343249363038611309], [15088007382495681292, "kuchiki", false, 9183891817400998185], [15609422047640926750, "toml", false, 18143949488324536827], [15622660310229662834, "walkdir", false, 4267858985654455119], [15932120279885307830, "memchr", false, 8346766755315681689], [16362055519698394275, "serde_json", false, 376776532189080624], [17146114186171651583, "infer", false, 9969932972114575562], [17155886227862585100, "glob", false, 12384589947144232082], [17186037756130803222, "phf", false, 6659243597246252888], [17990358020177143287, "quote", false, 9304775612473768952]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-e864d361cb1015e2\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}