{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5773842482151829625, "build_script_build", false, 9767626307719513684], [12092653563678505622, "build_script_build", false, 8037474702273569860], [16702348383442838006, "build_script_build", false, 6629645750320761838]], "local": [{"RerunIfChanged": {"output": "release\\build\\automa-flow-960c5282ac1ea3dc\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}